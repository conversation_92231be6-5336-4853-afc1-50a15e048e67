# Screenshot Bot

این ربات برای گرفتن اسکرین‌شات از لینک‌ها و ارسال آن‌ها به کاربران طراحی شده است.

## نصب و راه‌اندازی

### 1. نصب کتابخانه‌ها
```bash
pip install -r requirements.txt
playwright install chromium
```

### 2. ساخت سشن جدید
```bash
python setup_screenshot_bot.py
```

### 3. اجرای ربات
```bash
python screenshot_bot.py
```

## نحوه استفاده

برای استفاده از ربات، پیامی با فرمت زیر ارسال کنید:

```
screenshot
https://t.me/durov/336
452323199
1345
```

### توضیح فرمت:
- **خط اول**: کلمه "screenshot" (حتماً با حروف کوچک)
- **خط دوم**: لینک مورد نظر برای اسکرین‌شات
- **خط سوم**: شناسه کاربر (User ID) که اسکرین‌شات باید به او ارسال شود
- **خط چهارم**: شماره سفارش (Order)

## ویژگی‌ها

- ✅ گرفتن اسکرین‌شات تمام صفحه از هر لینک
- ✅ ارسال اسکرین‌شات با همان کپشن اصلی
- ✅ استفاده از سشن جدید (مجزا از سایر ربات‌ها)
- ✅ پردازش خودکار پیام‌های با فرمت صحیح
- ✅ مدیریت خطا و پیام‌های وضعیت

## نکات مهم

1. مطمئن شوید که Playwright نصب شده و مرورگر Chromium دانلود شده باشد
2. ربات فقط پیام‌هایی با فرمت دقیق مشخص شده را پردازش می‌کند
3. اسکرین‌شات‌ها به صورت موقت ذخیره شده و بعد از ارسال حذف می‌شوند
4. ربات از سشن جدید استفاده می‌کند و با سایر ربات‌ها تداخل ندارد
