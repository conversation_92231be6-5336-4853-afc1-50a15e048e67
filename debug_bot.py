import asyncio
from pyrogram import Client, filters
from pyrogram.types import Message

# اطلاعات API
api_id = 9644545
api_hash = "bd251ff230c39aae3d2b1ab3e21a4949"

# ساخت کلاینت
app = Client("screenshot_bot", api_id=api_id, api_hash=api_hash)

@app.on_message()
async def log_all_messages(client: Client, message: Message):
    """
    لاگ کردن همه پیام‌ها برای دیباگ
    """
    print("=" * 50)
    print("📨 پیام جدید دریافت شد!")
    print(f"👤 از: {message.from_user.first_name if message.from_user else 'نامشخص'}")
    print(f"🆔 User ID: {message.from_user.id if message.from_user else 'نامشخص'}")
    print(f"💬 نوع چت: {message.chat.type}")
    print(f"📝 متن پیام: {message.text if message.text else 'بدون متن'}")
    print("=" * 50)
    
    # پاسخ ساده
    try:
        await message.reply("✅ پیام دریافت شد!")
    except Exception as e:
        print(f"خطا در پاسخ: {e}")

async def main():
    """
    تابع اصلی
    """
    print("🚀 شروع ربات دیباگ...")
    await app.start()
    
    # دریافت اطلاعات کاربر
    me = await app.get_me()
    print(f"✅ ربات آماده!")
    print(f"👤 اکانت: {me.first_name}")
    print(f"🆔 User ID: {me.id}")
    print(f"📱 شماره: {me.phone_number}")
    print("\n🔍 منتظر پیام‌ها...")
    print("هر پیامی که بفرستید لاگ می‌شود")
    
    try:
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        print("\n🛑 توقف ربات...")
    finally:
        await app.stop()

if __name__ == "__main__":
    asyncio.run(main())
