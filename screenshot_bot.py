import asyncio
import re
import os
import tempfile
from pyrogram import Client, filters
from pyrogram.types import Message
from playwright.async_api import async_playwright

# اطلاعات API (همان اطلاعات قبلی)
api_id = 9644545
api_hash = "bd251ff230c39aae3d2b1ab3e21a4949"

# ساخت کلاینت جدید با نام سشن جدید
app = Client("screenshot_bot", api_id=api_id, api_hash=api_hash)

async def take_screenshot(url: str) -> str:
    """
    گرفتن اسکرین‌شات از URL و بازگرداندن مسیر فایل
    """
    try:
        async with async_playwright() as p:
            # راه‌اندازی مرورگر
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            # تنظیم اندازه صفحه
            await page.set_viewport_size({"width": 1280, "height": 720})
            
            # رفتن به URL
            await page.goto(url, wait_until="networkidle")
            
            # ساخت فایل موقت برای اسکرین‌شات
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            screenshot_path = temp_file.name
            temp_file.close()
            
            # گرفتن اسکرین‌شات
            await page.screenshot(path=screenshot_path, full_page=True)
            
            await browser.close()
            return screenshot_path
            
    except Exception as e:
        print(f"Error taking screenshot: {e}")
        return None

def parse_screenshot_message(text: str) -> dict:
    """
    پارس کردن پیام با فرمت مشخص شده
    """
    lines = text.strip().split('\n')
    
    if len(lines) < 4:
        return None
    
    # بررسی اینکه خط اول "screenshot" باشد
    if lines[0].strip().lower() != "screenshot":
        return None
    
    try:
        return {
            "command": lines[0].strip(),
            "link": lines[1].strip(),
            "user": int(lines[2].strip()),
            "order": int(lines[3].strip())
        }
    except (ValueError, IndexError):
        return None

@app.on_message(filters.text)
async def handle_screenshot_request(client: Client, message: Message):
    """
    هندل کردن درخواست‌های اسکرین‌شات
    """
    try:
        # پارس کردن پیام
        parsed = parse_screenshot_message(message.text)

        if not parsed:
            return  # اگر فرمت درست نباشد، نادیده بگیر

        print(f"Screenshot request received:")
        print(f"Link: {parsed['link']}")
        print(f"User: {parsed['user']}")
        print(f"Order: {parsed['order']}")

        # ارسال پیام در حال پردازش
        processing_msg = await message.reply("🔄 در حال گرفتن اسکرین‌شات...")

        # گرفتن اسکرین‌شات
        screenshot_path = await take_screenshot(parsed['link'])

        if screenshot_path and os.path.exists(screenshot_path):
            # ساخت کپشن با همان متن اصلی
            caption = message.text

            # برای تست: ذخیره اسکرین‌شات با نام مشخص
            test_screenshot_path = f"screenshot_test_{parsed['order']}.png"
            os.rename(screenshot_path, test_screenshot_path)

            # ویرایش پیام پردازش
            await processing_msg.edit(f"✅ اسکرین‌شات ذخیره شد: {test_screenshot_path}")

            print(f"Screenshot saved as: {test_screenshot_path}")

            # اگر می‌خواهید به کاربر ارسال کنید، این خطوط را uncomment کنید:
            # await client.send_photo(
            #     chat_id=parsed['user'],
            #     photo=test_screenshot_path,
            #     caption=caption
            # )
            # print(f"Screenshot sent to user {parsed['user']}")

        else:
            await processing_msg.edit("❌ خطا در گرفتن اسکرین‌شات")
            print("Failed to take screenshot")

    except Exception as e:
        print(f"Error in handle_screenshot_request: {e}")
        try:
            await message.reply(f"❌ خطا: {str(e)}")
        except:
            pass

async def main():
    """
    تابع اصلی برای راه‌اندازی ربات
    """
    print("Starting screenshot bot...")
    await app.start()
    print("Bot is running! Send messages in this format:")
    print("screenshot")
    print("https://example.com")
    print("123456789")
    print("1")
    print("\nPress Ctrl+C to stop")
    
    try:
        await asyncio.Event().wait()  # نگه داشتن ربات زنده
    except KeyboardInterrupt:
        print("\nStopping bot...")
    finally:
        await app.stop()

if __name__ == "__main__":
    asyncio.run(main())
