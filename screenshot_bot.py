import asyncio
import re
import os
import tempfile
from pyrogram import Client, filters
from pyrogram.types import Message
from playwright.async_api import async_playwright

# اطلاعات API (همان اطلاعات قبلی)
api_id = 9644545
api_hash = "bd251ff230c39aae3d2b1ab3e21a4949"

# ساخت کلاینت جدید با نام سشن جدید
app = Client("screenshot_bot", api_id=api_id, api_hash=api_hash)

async def take_screenshot(url: str) -> str:
    """
    گرفتن اسکرین‌شات از URL و بازگرداندن مسیر فایل
    """
    try:
        async with async_playwright() as p:
            # راه‌اندازی مرورگر
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            # تنظیم اندازه صفحه
            await page.set_viewport_size({"width": 1280, "height": 720})
            
            # رفتن به URL
            await page.goto(url, wait_until="networkidle")
            
            # ساخت فایل موقت برای اسکرین‌شات
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            screenshot_path = temp_file.name
            temp_file.close()
            
            # گرفتن اسکرین‌شات
            await page.screenshot(path=screenshot_path, full_page=True)
            
            await browser.close()
            return screenshot_path
            
    except Exception as e:
        print(f"Error taking screenshot: {e}")
        return None

def parse_screenshot_message(text: str) -> dict:
    """
    پارس کردن پیام با فرمت مشخص شده
    """
    lines = text.strip().split('\n')
    
    if len(lines) < 4:
        return None
    
    # بررسی اینکه خط اول "screenshot" باشد
    if lines[0].strip().lower() != "screenshot":
        return None
    
    try:
        return {
            "command": lines[0].strip(),
            "link": lines[1].strip(),
            "user": int(lines[2].strip()),
            "order": int(lines[3].strip())
        }
    except (ValueError, IndexError):
        return None

@app.on_message(filters.text & filters.private)
async def handle_screenshot_request(client: Client, message: Message):
    """
    هندل کردن درخواست‌های اسکرین‌شات
    """
    try:
        # اعلام دریافت پیام
        print("📨 یه پیام رسیده!")

        # پارس کردن پیام
        parsed = parse_screenshot_message(message.text)

        if not parsed:
            print("❌ پیام درست نیست - نادیده گرفته شد")
            return  # اگر فرمت درست نباشد، نادیده بگیر

        # اعلام پیام درست
        print("✅ پیامه درست رسیده!")
        print(f"📋 جزئیات:")
        print(f"   🔗 Link: {parsed['link']}")
        print(f"   👤 User: {parsed['user']}")
        print(f"   📦 Order: {parsed['order']}")

        # ارسال پیام در حال پردازش
        processing_msg = await message.reply("🔄 در حال گرفتن اسکرین‌شات...")

        # ساخت پوشه shots اگر وجود نداشته باشد
        shots_dir = "shots"
        if not os.path.exists(shots_dir):
            os.makedirs(shots_dir)
            print(f"📁 پوشه {shots_dir} ساخته شد")

        # گرفتن اسکرین‌شات
        print("📸 در حال گرفتن اسکرین‌شات...")
        screenshot_path = await take_screenshot(parsed['link'])

        if screenshot_path and os.path.exists(screenshot_path):
            # ساخت کپشن با همان متن اصلی
            caption = message.text

            # ذخیره اسکرین‌شات در پوشه shots
            final_screenshot_path = os.path.join(shots_dir, f"screenshot_{parsed['order']}.png")
            os.rename(screenshot_path, final_screenshot_path)

            print(f"💾 عکس ذخیره شد: {final_screenshot_path}")

            # ارسال اسکرین‌شات به کاربر مشخص شده
            await client.send_photo(
                chat_id=parsed['user'],
                photo=final_screenshot_path,
                caption=caption
            )

            # ویرایش پیام پردازش
            await processing_msg.edit("✅ اسکرین‌شات با موفقیت ارسال شد!")

            print(f"📤 اسکرین‌شات به کاربر {parsed['user']} ارسال شد")

        else:
            await processing_msg.edit("❌ خطا در گرفتن اسکرین‌شات")
            print("❌ خطا در گرفتن اسکرین‌شات")

    except Exception as e:
        print(f"💥 خطا در handle_screenshot_request: {e}")
        try:
            await message.reply(f"❌ خطا: {str(e)}")
        except:
            pass

async def main():
    """
    تابع اصلی برای راه‌اندازی ربات
    """
    print("🚀 Starting screenshot bot...")
    await app.start()

    # دریافت اطلاعات کاربر
    me = await app.get_me()
    print(f"✅ Bot is running!")
    print(f"👤 Account: {me.first_name} (@{me.username})")
    print(f"📱 Phone: {me.phone_number}")
    print(f"🆔 User ID: {me.id}")
    print("\n📋 Send messages in this format:")
    print("screenshot")
    print("https://example.com")
    print("*********")
    print("1")
    print("\n🔍 Waiting for messages...")
    print("Press Ctrl+C to stop")

    try:
        await asyncio.Event().wait()  # نگه داشتن ربات زنده
    except KeyboardInterrupt:
        print("\n🛑 Stopping bot...")
    finally:
        await app.stop()

if __name__ == "__main__":
    asyncio.run(main())
