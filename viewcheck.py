import asyncio
from pyrogram import Client

# استفاده از فایل سشن
app = Client("my_account")

# اطلاعات کانال‌ها
channel_username = "@alonp_band"  # آیدی کانال اصلی
source_channel_id = -*************  # چت آیدی کانال منبع
source_message_id = 36  # آیدی پیام جایگزین

# ذخیره بازدیدهای قبلی
previous_views = {}

async def check_and_replace_posts():
    global previous_views

    async with app:
        while True:
            try:
                messages = []

                # دریافت ۵ پست آخر (یا کمتر، بدون خطا)
                async for message in app.get_chat_history(channel_username, limit=5):
                    if message.views is not None and message.id is not None:  # فقط پیام‌هایی که ویو دارند
                        messages.append((message.id, message.views))

                for id, views in messages:
                    # مقایسه با مقدار قبلی
                    if id in previous_views:
                        view_diff = views - previous_views[id]
                        print(f"Post {id}: View change = {view_diff}")

                        if view_diff > 50:
                            # حذف پست
                            await app.delete_messages(channel_username, id)
                            print(f"Post {id} deleted (View increase: {view_diff})")

                            # گرفتن پست جایگزین و ارسال به کانال اصلی
                            msg = await app.get_messages(source_channel_id, source_message_id)
                            if msg:
                                await app.copy_message(channel_username, source_channel_id, source_message_id)
                                print(f"Replacement post from {source_channel_id} sent.")

                    # ذخیره مقدار جدید
                    previous_views[id] = views

            except Exception as e:
                print(f"Error occurred: {e}")

            await asyncio.sleep(60)  # اجرای هر ۶۰ ثانیه

# اجرای ربات
app.run(check_and_replace_posts())
