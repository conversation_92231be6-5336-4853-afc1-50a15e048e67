import asyncio
from pyrogram import Client

# استفاده از فایل سشن
app = Client("my_account")

# کانال‌هایی که باید بررسی بشن و پست جایگزین مخصوص خودشون رو دارن
channel_configs = {
    "@alonp_band": {
        "replacement_channel_id": -*************,  # @testarcreport
        "replacement_message_id": 36
    },
    "@mutbn_rios": {
        "replacement_channel_id": -*************,  # @arcstoragesdata
        "replacement_message_id": 17
    }
}

# ذخیره بازدیدهای قبلی برای هر پست
previous_views = {}

async def check_and_replace_posts():
    global previous_views

    async with app:
        while True:
            try:
                for channel_username, config in channel_configs.items():
                    messages = []

                    # دریافت آخرین ۵ پیام
                    async for message in app.get_chat_history(channel_username, limit=5):
                        if message.views is not None and message.id is not None:
                            messages.append((message.id, message.views))

                    for id, views in messages:
                        key = f"{channel_username}_{id}"

                        if key in previous_views:
                            view_diff = views - previous_views[key]
                            print(f"[{channel_username}] Post {id}: View change = {view_diff}")

                            if view_diff > 50:
                                # حذف پست
                                await app.delete_messages(channel_username, id)
                                print(f"[{channel_username}] Post {id} deleted")

                                # ارسال پست جایگزین مخصوص همین کانال
                                await app.copy_message(
                                    channel_username,
                                    config["replacement_channel_id"],
                                    config["replacement_message_id"]
                                )
                                print(f"Replacement post sent to {channel_username}")

                        previous_views[key] = views

            except Exception as e:
                print(f"Error occurred: {e}")

            await asyncio.sleep(60)

# اجرای ربات
app.run(check_and_replace_posts())
