import asyncio
from pyrogram import Client

# اطلاعات API
api_id = 9644545
api_hash = "bd251ff230c39aae3d2b1ab3e21a4949"

# استفاده از سشن موجود
app = Client("screenshot_bot", api_id=api_id, api_hash=api_hash)

async def send_test_message():
    """
    ارسال پیام تست برای ربات اسکرین‌شات
    """
    async with app:
        # پیام تست با فرمت صحیح
        test_message = """screenshot
https://www.google.com
123456789
1001"""
        
        # ارسال پیام به خودمان (Saved Messages)
        await app.send_message("me", test_message)
        print("Test message sent!")
        print("Message content:")
        print(test_message)

if __name__ == "__main__":
    asyncio.run(send_test_message())
